NODE_ENV=development

APP_NAME="api-mirror"
PORT=3333
API_URL="http://localhost:3333"
APP_URL="http://localhost:3000"
# CONF
ENABLE_LOGGING=false
# DATABASE
DATABASE_URL="postgresql://docker:docker@localhost:5432/mirror?schema=public"
# JWT
JWT_PRIVATE_KEY="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"
JWT_PUBLIC_KEY="LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE1Z1pJLzB3OC9sN0tGekZnRHZkWgpWOU1qNVRkb0paRFN6bWkySXZSTTBmWi9kR0dWQ0Z2TmVRWm9RMmVLYm54bTZwenNERWpBdW1OV3R3VzR4bmVTCm5Bb1Z5UEFxMy9Gb29nRlJIalFpei9Qd3BTZG5XSjBhK0Noa1hJc1ZUa1g2WEVrN0dmSFJLWFBKSGlJc0FnNW8Kd0kxd2pCSnhNN2VIUUg2WXgrNXNTL1gyZHJhSzNHOGttajB3bTVmMTE1YjBncFFsakRnOTh6d1pCMHBTanlYaQpvZUJab0JRbVQrNHZ4KzRxZHdDSWExdjZKYjFXQWppemUxa3Q4Mllwc3REZEZJZFMxZitWUTNMM0VTTEFqMjdjCks2Y2V4ak1aRThBMjZZd2lWRkFOQ01XRktXRTdzNWNLaXB0ak8zRXd2RWg2NmxOME51eEVDOTIyY3VNMEU2WFQKRXdJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t"
JWT_EXPIRES_IN=30m
JWT_REFRESH_EXPIRES_IN=24h
# Throttle
THROTTLE_TTL=60000
THROTTLE_LIMIT=10
# Email
MAIL_DRIVER=smtp
MAIL_HOST=smtps://<EMAIL>:<EMAIL>
MAIL_PORT=2525
MAIL_USER=<EMAIL>
MAIL_PASS=pass  
MAIL_FROM="No Reply <<EMAIL>>" 
AWS_REGION=us-east-1 
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY_ID 
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_ACCESS_KEY 
NODE_ENV=development
# SMS API https://smsmarket.docs.apiary.io/
API_SMS_URL=https://api.smsmarket.com.br/webservice-rest/send-single
API_SMS_AUTH="cho -n usuario:senha | base64"